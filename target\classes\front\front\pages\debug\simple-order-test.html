<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简单订单测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../layui/css/layui.css">
</head>
<body>
    <div class="layui-container" style="padding: 20px;">
        <h2>简单订单提交测试</h2>
        
        <form class="layui-form" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">用户ID</label>
                <div class="layui-input-block">
                    <input type="text" id="userId" name="userId" placeholder="请输入用户ID" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">地址ID</label>
                <div class="layui-input-block">
                    <input type="text" id="addressId" name="addressId" placeholder="请输入地址ID" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">支付方式</label>
                <div class="layui-input-block">
                    <select id="paymentType" name="paymentType">
                        <option value="1">余额支付</option>
                        <option value="2">积分支付</option>
                    </select>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">商品数据</label>
                <div class="layui-input-block">
                    <textarea id="shangpins" name="shangpins" placeholder="请输入商品JSON数据" class="layui-textarea">[{"id":1,"shangpinId":1,"buyNumber":1}]</textarea>
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" onclick="submitOrder()">提交订单</button>
                    <button type="button" class="layui-btn layui-btn-primary" onclick="loadFromStorage()">从存储加载</button>
                </div>
            </div>
        </form>
        
        <fieldset class="layui-elem-field">
            <legend>响应结果</legend>
            <div class="layui-field-box">
                <pre id="result" style="background: #f2f2f2; padding: 10px; min-height: 100px;"></pre>
            </div>
        </fieldset>
    </div>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['layer', 'http', 'form'], function(){
            var layer = layui.layer;
            var http = layui.http;
            var form = layui.form;
            
            // 从localStorage加载数据
            window.loadFromStorage = function() {
                document.getElementById('userId').value = localStorage.getItem('userid') || '';
                document.getElementById('shangpins').value = localStorage.getItem('shangpins') || '[{"id":1,"shangpinId":1,"buyNumber":1}]';
                
                // 设置默认地址ID
                document.getElementById('addressId').value = '1';
                form.render();
            }
            
            // 提交订单
            window.submitOrder = function() {
                var userId = document.getElementById('userId').value;
                var addressId = document.getElementById('addressId').value;
                var paymentType = document.getElementById('paymentType').value;
                var shangpins = document.getElementById('shangpins').value;
                
                if(!userId) {
                    layer.msg('请输入用户ID');
                    return;
                }
                
                if(!addressId) {
                    layer.msg('请输入地址ID');
                    return;
                }
                
                // 验证商品数据格式
                try {
                    JSON.parse(shangpins);
                } catch(e) {
                    layer.msg('商品数据格式错误');
                    return;
                }
                
                var data = {
                    yonghuId: userId,
                    addressId: addressId,
                    shangpinOrderPaymentTypes: paymentType,
                    shangpins: shangpins
                };
                
                document.getElementById('result').textContent = '正在提交订单...\n请求数据: ' + JSON.stringify(data, null, 2);
                
                // 设置用户ID到localStorage（模拟登录状态）
                localStorage.setItem('userid', userId);
                
                // 先测试用户登录状态
                http.request('yonghu/session', 'GET', {}, function(sessionRes) {
                    document.getElementById('result').textContent += '用户会话检查: ' + JSON.stringify(sessionRes, null, 2) + '\n\n';

                    // 然后提交订单
                    http.request('shangpinOrder/order', 'POST', data, function(res) {
                        document.getElementById('result').textContent += '订单提交成功!\n响应数据: ' + JSON.stringify(res, null, 2);
                        layer.msg('订单提交成功!', {icon: 1});
                    });
                });
            }
            
            // 页面加载时自动加载数据
            loadFromStorage();
        });
    </script>
</body>
</html>
