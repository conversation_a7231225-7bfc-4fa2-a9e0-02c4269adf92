<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单提交调试页面</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../layui/css/layui.css">
</head>
<body>
    <div class="layui-container" style="padding: 20px;">
        <h2>订单提交调试工具</h2>
        
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md6">
                <fieldset class="layui-elem-field">
                    <legend>当前状态检查</legend>
                    <div class="layui-field-box">
                        <p>用户ID: <span id="userId"></span></p>
                        <p>用户表: <span id="userTable"></span></p>
                        <p>Token: <span id="token"></span></p>
                        <p>购物车数据: <span id="cartData"></span></p>
                    </div>
                </fieldset>
                
                <fieldset class="layui-elem-field">
                    <legend>测试接口连接</legend>
                    <div class="layui-field-box">
                        <button class="layui-btn" onclick="testConnection()">测试服务器连接</button>
                        <button class="layui-btn layui-btn-normal" onclick="testSession()">测试用户会话</button>
                        <button class="layui-btn layui-btn-warm" onclick="testOrderAPI()">测试订单接口</button>
                    </div>
                </fieldset>
            </div>
            
            <div class="layui-col-md6">
                <fieldset class="layui-elem-field">
                    <legend>调试日志</legend>
                    <div class="layui-field-box">
                        <textarea id="debugLog" class="layui-textarea" style="height: 300px;" readonly></textarea>
                        <button class="layui-btn layui-btn-sm" onclick="clearLog()">清空日志</button>
                    </div>
                </fieldset>
            </div>
        </div>
    </div>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['layer', 'http'], function(){
            var layer = layui.layer;
            var http = layui.http;
            
            // 显示当前状态
            function updateStatus() {
                document.getElementById('userId').textContent = localStorage.getItem('userid') || '未设置';
                document.getElementById('userTable').textContent = localStorage.getItem('userTable') || '未设置';
                document.getElementById('token').textContent = localStorage.getItem('Token') || '未设置';
                
                var cartData = localStorage.getItem('shangpins');
                if(cartData) {
                    try {
                        var parsed = JSON.parse(cartData);
                        document.getElementById('cartData').textContent = parsed.length + ' 个商品';
                    } catch(e) {
                        document.getElementById('cartData').textContent = '数据格式错误';
                    }
                } else {
                    document.getElementById('cartData').textContent = '无数据';
                }
            }
            
            // 添加日志
            function addLog(message) {
                var log = document.getElementById('debugLog');
                var timestamp = new Date().toLocaleTimeString();
                log.value += '[' + timestamp + '] ' + message + '\n';
                log.scrollTop = log.scrollHeight;
            }
            
            // 测试服务器连接
            window.testConnection = function() {
                addLog('测试服务器连接...');
                http.request('config/list', 'GET', {page: 1, limit: 1}, function(res) {
                    addLog('✓ 服务器连接正常，响应: ' + JSON.stringify(res));
                });
            }
            
            // 测试用户会话
            window.testSession = function() {
                addLog('测试用户会话...');
                var table = localStorage.getItem('userTable');
                if(!table) {
                    addLog('✗ 用户未登录，userTable为空');
                    return;
                }
                
                http.request(table + '/session', 'GET', {}, function(res) {
                    addLog('✓ 用户会话正常，响应: ' + JSON.stringify(res));
                });
            }
            
            // 测试订单接口
            window.testOrderAPI = function() {
                addLog('测试订单接口...');
                
                // 构造测试数据
                var testData = {
                    addressId: 1,
                    shangpins: '[{"id":1,"shangpinId":1,"buyNumber":1}]',
                    yonghuId: localStorage.getItem('userid'),
                    shangpinOrderPaymentTypes: 1
                };
                
                addLog('发送测试数据: ' + JSON.stringify(testData));
                
                http.request('shangpinOrder/order', 'POST', testData, function(res) {
                    addLog('✓ 订单接口响应: ' + JSON.stringify(res));
                });
            }
            
            // 清空日志
            window.clearLog = function() {
                document.getElementById('debugLog').value = '';
            }
            
            // 初始化
            updateStatus();
            addLog('调试工具已加载');
            
            // 每5秒更新一次状态
            setInterval(updateStatus, 5000);
        });
    </script>
</body>
</html>
