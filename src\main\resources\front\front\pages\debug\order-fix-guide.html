<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单提交问题修复指南</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../layui/css/layui.css">
</head>
<body>
    <div class="layui-container" style="padding: 20px;">
        <h2>订单提交问题修复指南</h2>
        
        <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
            <ul class="layui-tab-title">
                <li class="layui-this">问题诊断</li>
                <li>修复步骤</li>
                <li>测试工具</li>
            </ul>
            <div class="layui-tab-content">
                <!-- 问题诊断 -->
                <div class="layui-tab-item layui-show">
                    <h3>常见问题诊断</h3>
                    
                    <fieldset class="layui-elem-field">
                        <legend>1. 检查用户登录状态</legend>
                        <div class="layui-field-box">
                            <button class="layui-btn layui-btn-sm" onclick="checkLoginStatus()">检查登录状态</button>
                            <div id="loginStatus" style="margin-top: 10px;"></div>
                        </div>
                    </fieldset>
                    
                    <fieldset class="layui-elem-field">
                        <legend>2. 检查购物车数据</legend>
                        <div class="layui-field-box">
                            <button class="layui-btn layui-btn-sm" onclick="checkCartData()">检查购物车数据</button>
                            <div id="cartStatus" style="margin-top: 10px;"></div>
                        </div>
                    </fieldset>
                    
                    <fieldset class="layui-elem-field">
                        <legend>3. 检查收货地址</legend>
                        <div class="layui-field-box">
                            <button class="layui-btn layui-btn-sm" onclick="checkAddress()">检查收货地址</button>
                            <div id="addressStatus" style="margin-top: 10px;"></div>
                        </div>
                    </fieldset>
                    
                    <fieldset class="layui-elem-field">
                        <legend>4. 测试网络连接</legend>
                        <div class="layui-field-box">
                            <button class="layui-btn layui-btn-sm" onclick="testNetwork()">测试网络连接</button>
                            <div id="networkStatus" style="margin-top: 10px;"></div>
                        </div>
                    </fieldset>
                </div>
                
                <!-- 修复步骤 -->
                <div class="layui-tab-item">
                    <h3>修复步骤</h3>
                    
                    <div class="layui-collapse" lay-filter="test">
                        <div class="layui-colla-item">
                            <h2 class="layui-colla-title">步骤1：重新登录</h2>
                            <div class="layui-colla-content">
                                <p>如果Token过期，请重新登录：</p>
                                <ol>
                                    <li>点击退出登录</li>
                                    <li>重新输入用户名和密码登录</li>
                                    <li>确认登录成功后再次尝试提交订单</li>
                                </ol>
                                <button class="layui-btn layui-btn-sm" onclick="logout()">退出登录</button>
                            </div>
                        </div>
                        
                        <div class="layui-colla-item">
                            <h2 class="layui-colla-title">步骤2：清理购物车数据</h2>
                            <div class="layui-colla-content">
                                <p>如果购物车数据异常，请清理并重新添加：</p>
                                <ol>
                                    <li>清空购物车</li>
                                    <li>重新添加商品到购物车</li>
                                    <li>确认商品信息正确后提交订单</li>
                                </ol>
                                <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="clearCart()">清空购物车</button>
                            </div>
                        </div>
                        
                        <div class="layui-colla-item">
                            <h2 class="layui-colla-title">步骤3：检查收货地址</h2>
                            <div class="layui-colla-content">
                                <p>确保有有效的收货地址：</p>
                                <ol>
                                    <li>进入个人中心</li>
                                    <li>添加或编辑收货地址</li>
                                    <li>确保地址信息完整</li>
                                </ol>
                                <button class="layui-btn layui-btn-sm" onclick="goToAddress()">管理收货地址</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 测试工具 -->
                <div class="layui-tab-item">
                    <h3>测试工具</h3>
                    
                    <form class="layui-form" action="">
                        <div class="layui-form-item">
                            <label class="layui-form-label">测试订单</label>
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn" onclick="testOrder()">提交测试订单</button>
                                <button type="button" class="layui-btn layui-btn-primary" onclick="mockData()">生成测试数据</button>
                            </div>
                        </div>
                    </form>
                    
                    <fieldset class="layui-elem-field">
                        <legend>测试结果</legend>
                        <div class="layui-field-box">
                            <pre id="testResult" style="background: #f2f2f2; padding: 10px; min-height: 200px; max-height: 400px; overflow-y: auto;"></pre>
                        </div>
                    </fieldset>
                </div>
            </div>
        </div>
    </div>

    <script src="../../layui/layui.js"></script>
    <script>
        layui.use(['layer', 'http', 'element', 'collapse'], function(){
            var layer = layui.layer;
            var http = layui.http;
            var element = layui.element;
            var collapse = layui.collapse;
            
            // 添加日志
            function addLog(message, type = 'info') {
                var log = document.getElementById('testResult');
                var timestamp = new Date().toLocaleTimeString();
                var prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
                log.textContent += `[${timestamp}] ${prefix} ${message}\n`;
                log.scrollTop = log.scrollHeight;
            }
            
            // 检查登录状态
            window.checkLoginStatus = function() {
                var status = document.getElementById('loginStatus');
                var userId = localStorage.getItem('userid');
                var token = localStorage.getItem('Token');
                var userTable = localStorage.getItem('userTable');
                
                if(!userId || !token || !userTable) {
                    status.innerHTML = '<span style="color: red;">❌ 用户未登录或登录信息不完整</span>';
                    addLog('用户未登录或登录信息不完整', 'error');
                    return;
                }
                
                // 测试会话
                http.request(userTable + '/session', 'GET', {}, function(res) {
                    status.innerHTML = '<span style="color: green;">✅ 用户登录状态正常</span>';
                    addLog('用户登录状态正常', 'success');
                });
            }
            
            // 检查购物车数据
            window.checkCartData = function() {
                var status = document.getElementById('cartStatus');
                var cartData = localStorage.getItem('shangpins');
                
                if(!cartData || cartData === 'null' || cartData === '[]') {
                    status.innerHTML = '<span style="color: red;">❌ 购物车为空</span>';
                    addLog('购物车为空', 'error');
                    return;
                }
                
                try {
                    var data = JSON.parse(cartData);
                    if(Array.isArray(data) && data.length > 0) {
                        status.innerHTML = `<span style="color: green;">✅ 购物车有 ${data.length} 个商品</span>`;
                        addLog(`购物车有 ${data.length} 个商品`, 'success');
                    } else {
                        status.innerHTML = '<span style="color: red;">❌ 购物车数据格式错误</span>';
                        addLog('购物车数据格式错误', 'error');
                    }
                } catch(e) {
                    status.innerHTML = '<span style="color: red;">❌ 购物车数据解析失败</span>';
                    addLog('购物车数据解析失败: ' + e.message, 'error');
                }
            }
            
            // 检查收货地址
            window.checkAddress = function() {
                var status = document.getElementById('addressStatus');
                var userId = localStorage.getItem('userid');
                
                if(!userId) {
                    status.innerHTML = '<span style="color: red;">❌ 用户未登录</span>';
                    return;
                }
                
                http.request('address/page', 'GET', {yonghuId: userId}, function(res) {
                    if(res.data && res.data.list && res.data.list.length > 0) {
                        status.innerHTML = `<span style="color: green;">✅ 有 ${res.data.list.length} 个收货地址</span>`;
                        addLog(`有 ${res.data.list.length} 个收货地址`, 'success');
                    } else {
                        status.innerHTML = '<span style="color: red;">❌ 没有收货地址</span>';
                        addLog('没有收货地址', 'error');
                    }
                });
            }
            
            // 测试网络连接
            window.testNetwork = function() {
                var status = document.getElementById('networkStatus');
                
                http.request('config/list', 'GET', {page: 1, limit: 1}, function(res) {
                    status.innerHTML = '<span style="color: green;">✅ 网络连接正常</span>';
                    addLog('网络连接正常', 'success');
                });
            }
            
            // 退出登录
            window.logout = function() {
                localStorage.clear();
                layer.msg('已清除登录信息，请重新登录', {icon: 1});
                setTimeout(() => {
                    window.location.href = '../login/login.html';
                }, 2000);
            }
            
            // 清空购物车
            window.clearCart = function() {
                localStorage.removeItem('shangpins');
                layer.msg('购物车已清空', {icon: 1});
                addLog('购物车已清空', 'success');
            }
            
            // 跳转到地址管理
            window.goToAddress = function() {
                window.location.href = '../address/list.html';
            }
            
            // 生成测试数据
            window.mockData = function() {
                // 模拟登录数据
                localStorage.setItem('userid', '1');
                localStorage.setItem('userTable', 'yonghu');
                localStorage.setItem('Token', 'test-token-' + Date.now());
                
                // 模拟购物车数据
                var mockCart = [
                    {
                        id: 1,
                        shangpinId: 1,
                        buyNumber: 1,
                        shangpinName: '测试商品',
                        shangpinNewMoney: 100
                    }
                ];
                localStorage.setItem('shangpins', JSON.stringify(mockCart));
                
                layer.msg('测试数据已生成', {icon: 1});
                addLog('测试数据已生成', 'success');
            }
            
            // 测试订单提交
            window.testOrder = function() {
                addLog('开始测试订单提交...');
                
                var testData = {
                    addressId: 1,
                    shangpins: localStorage.getItem('shangpins') || '[{"id":1,"shangpinId":1,"buyNumber":1}]',
                    yonghuId: localStorage.getItem('userid') || '1',
                    shangpinOrderPaymentTypes: 1
                };
                
                addLog('测试数据: ' + JSON.stringify(testData));
                
                http.request('shangpinOrder/order', 'POST', testData, function(res) {
                    addLog('订单提交成功: ' + JSON.stringify(res), 'success');
                    layer.msg('测试订单提交成功!', {icon: 1});
                });
            }
            
            // 页面加载时自动检查
            setTimeout(() => {
                addLog('页面加载完成，开始自动检查...');
                checkLoginStatus();
                checkCartData();
                checkAddress();
                testNetwork();
            }, 1000);
        });
    </script>
</body>
</html>
